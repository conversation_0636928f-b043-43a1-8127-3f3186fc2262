file,line,snippet,mock_type
config.py,60,"""检测存储类型：mock_s3, real_s3, 或 local""",storage_type_detection
config.py,62,"if os.getenv('USE_MOCK_S3', 'false').lower() == 'true':",env_var_USE_MOCK_S3
config.py,63,return 'mock_s3',mock_s3_type
config.py,70,"USE_S3 = STORAGE_TYPE in ['mock_s3', 'real_s3']",storage_type_check
config.py,219,"elif IS_UNIX and STORAGE_TYPE in ['mock_s3', 'real_s3']:",platform_storage_check