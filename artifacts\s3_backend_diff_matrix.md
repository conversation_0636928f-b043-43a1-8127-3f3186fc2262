# S3 Backend Comparison Matrix

## Overview
Comparison between legacy implementation (parallel_experiments_method1\src_1stgpu_1230_0801) and current repository.

## S3 Backend Implementation Comparison

| Feature | Legacy Implementation | Current Implementation | Notes |
|---------|----------------------|----------------------|-------|
| **S3 Library** | s3fs | None | Current repo has no S3 dependencies |
| **Mock Backend** | USE_MOCK_S3 env var (no actual implementation) | None | Legacy has placeholder, no real mock |
| **Storage Detection** | Automatic based on path prefix | N/A | Legacy checks for 's3://' prefix |
| **Credential Handling** | Default AWS credential chain | N/A | Legacy uses boto3/s3fs defaults |

## Read Operations

| Operation | Legacy | Current | Gap Analysis |
|-----------|---------|---------|--------------|
| read_parquet | s3fs via pd.read_parquet() | Local only | Need S3 support |
| list_parquet_files | s3fs.glob() | Local glob | Need S3 support |
| path_exists | s3fs.exists() | os.path.exists() | Need S3 support |

## Write Operations

| Operation | Legacy | Current | Gap Analysis |
|-----------|---------|---------|--------------|
| save_parquet | Not implemented | Not implemented | Both missing |
| save_npy | Local only (np.save) | common_io.save_npy() (local only) | Need S3 support |
| write_json | Local only (json.dump) | Direct json.dump() | Need S3 support |
| upload_file | Not implemented | Not implemented | Both missing |

## Mock/Test Infrastructure

| Component | Legacy | Current | Gap Analysis |
|-----------|---------|---------|--------------|
| Mock Backend | USE_MOCK_S3 flag only | None | Neither has actual mock |
| Test Data | Local test data | Local test data | Both use local files |
| CI/CD Support | No mock tests | No S3 tests | Both lack S3 testing |

## Key Findings

1. **Legacy S3 Implementation**:
   - Uses s3fs for all S3 operations
   - Has connection pooling and retry logic
   - Fork-safe implementation with process-local instances
   - No actual mock implementation despite USE_MOCK_S3 flag

2. **Current Repository**:
   - No S3 support at all
   - All I/O operations are local-only
   - common_io module exists but empty (only TODOs)

3. **Major Gaps**:
   - Neither repository has S3 write operations implemented
   - Neither has a working mock S3 backend
   - Current repo needs complete S3 infrastructure

4. **Migration Path**:
   - Can adopt legacy's s3fs approach
   - Need to implement mock backend (moto recommended)
   - Need to extend common_io with S3 support