# Legacy S3 Backend Discovery Report

## Executive Summary

This report analyzes the S3 implementation in the legacy codebase (`parallel_experiments_method1\src_1stgpu_1230_0801`) to understand how it handles S3 operations and switching between mock/real backends.

## Key Findings

### 1. S3 Implementation Architecture

**Legacy S3 Stack:**
- **Library**: Uses `s3fs` for all S3 operations
- **Dependencies**: `s3fs`, `boto3`, `pandas`
- **Design Pattern**: Fork-safe with process-local instances
- **Features**: Connection pooling, retry logic, detailed logging

**Supported Operations:**
- ✅ Read: `read_parquet()`, `list_files()`, `exists()`
- ❌ Write: No S3 write operations implemented
- ❌ Mock: USE_MOCK_S3 flag exists but no implementation

### 2. Mock/Real Switching Mechanism

**Current Legacy Approach:**
```python
# In config.py
if os.getenv('USE_MOCK_S3', 'false').lower() == 'true':
    return 'mock_s3'
else:
    return 'real_s3'
```

**Critical Issue**: The mock mode is a placeholder - no actual mock backend exists. When `USE_MOCK_S3=true`, the code still uses real s3fs, just labeled as "mock".

### 3. S3 Write Operations Gap

Neither the legacy nor current repository implements S3 write operations:
- No `save_parquet()` to S3
- No `save_npy()` to S3  
- No `upload_file()` functionality
- All write operations are local-only

### 4. Credential Management

Legacy uses AWS SDK's default credential chain:
- IAM roles (on EC2)
- Environment variables (AWS_ACCESS_KEY_ID, etc.)
- AWS CLI configuration
- No explicit credential handling in code

## Comparison with Current Repository

| Aspect | Legacy | Current | Action Needed |
|--------|--------|---------|---------------|
| S3 Read | ✅ s3fs | ❌ None | Port legacy code |
| S3 Write | ❌ None | ❌ None | Implement new |
| Mock Backend | ❌ Placeholder | ❌ None | Implement moto |
| Credentials | ✅ AWS defaults | N/A | Keep legacy approach |

## Recommendations for Step 8.2

### 1. Adopt Legacy's S3 Read Implementation
- Port `s3_utils.py` core functionality
- Maintain fork-safe design
- Keep retry and connection pooling

### 2. Implement Missing S3 Write Operations
```python
# Extend common_io.py
def save_parquet(df, path, s3_config=None):
    if is_s3_path(path):
        # Use s3fs to write
    else:
        # Local write

def save_npy(array, path, s3_config=None):
    if is_s3_path(path):
        # Write to buffer, upload to S3
    else:
        # Local write
```

### 3. Implement Proper Mock Backend
- Use `moto` for S3 API mocking
- Local directory mapping: `s3://bucket/key` → `./_s3mock/bucket/key`
- Support all operations transparently

### 4. Unified Backend Switching
- Environment variable: `S3_BACKEND={auto|real|mock|skip}`
- Auto-detection based on credentials and platform
- Clear precedence rules (see `s3_backend_switch_design.md`)

## Implementation Priority

1. **Phase 1**: Port legacy S3 read operations
2. **Phase 2**: Add S3 write support to common_io
3. **Phase 3**: Implement moto-based mock backend
4. **Phase 4**: Add comprehensive S3 tests

## Risk Assessment

- **Low Risk**: Porting read operations (proven in legacy)
- **Medium Risk**: Write operations (new implementation)
- **Low Risk**: Mock backend (well-established pattern)
- **Critical**: Must maintain backward compatibility