file,line,snippet,api
s3_utils.py,16,import s3fs,s3fs
s3_utils.py,22,"logging.warning(""S3 dependencies not available. Install with: pip install s3fs boto3"")",s3fs/boto3
s3_utils.py,101,"raise ImportError(""S3 dependencies not available. Install with: pip install s3fs boto3"")",s3fs/boto3
s3_utils.py,158,self.fs = s3fs.S3FileSystem(**fs_kwargs),s3fs.S3FileSystem
s3_utils.py,318,fs = s3fs.S3FileSystem(**fs_kwargs),s3fs.S3FileSystem
s3_utils.py,384,df = pd.read_parquet(s3_path filesystem=process_local_fs **kwargs),pd.read_parquet
mlflow_config.py,145,torch.save(model.state_dict() model_path),torch.save