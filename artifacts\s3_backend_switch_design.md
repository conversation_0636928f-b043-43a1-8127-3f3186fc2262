# S3 Backend Switching Strategy Design

## Unified Backend Selection Logic

### Priority Order (Recommended)
1. **S3_BACKEND** environment variable (explicit override)
2. **Credential Detection** (AWS_ACCESS_KEY_ID presence)
3. **Platform Default** (Windows=mock, Linux/EC2=real)
4. **Fallback** (skip S3 tests)

### Environment Variables

| Variable | Values | Default | Description |
|----------|--------|---------|-------------|
| S3_BACKEND | auto, real, mock, skip | auto | Explicit backend selection |
| ENABLE_S3_TESTS | 0, 1 | 0 | Enable S3-dependent tests |
| MOCK_S3_ROOT | path | ./_s3mock/ | Local directory for mock S3 |
| IO_RETRY_MAX | int | 3 | Max retry attempts |
| IO_RETRY_BACKOFF | float | 1.0 | Initial retry delay (seconds) |
| IO_TIMEOUT | int | 30 | Operation timeout (seconds) |

### Backend Selection Pseudocode

```python
def get_s3_backend():
    # 1. Explicit override
    if S3_BACKEND := os.getenv('S3_BACKEND'):
        if S3_BACKEND in ['real', 'mock', 'skip']:
            return S3_BACKEND
    
    # 2. Auto-detection
    # Check for AWS credentials
    has_credentials = bool(os.getenv('AWS_ACCESS_KEY_ID'))
    
    # Check platform
    is_windows = platform.system() == 'Windows'
    is_ec2 = os.path.exists('/opt/aws/bin/ec2-metadata')
    
    # Decision logic
    if has_credentials or is_ec2:
        return 'real'
    elif is_windows:
        return 'mock'
    else:
        # Linux/Mac without credentials
        return 'mock'
```

### Implementation Strategy

1. **Real Backend (s3fs)**
   - Use legacy's s3fs implementation
   - Add write operations (to_parquet, save_npy)
   - Maintain fork-safe design

2. **Mock Backend (moto)**
   - Use moto for S3 API mocking
   - Create local directory mapping
   - Support all read/write operations

3. **Skip Mode**
   - Disable all S3-related functionality
   - Skip S3 tests in test suite
   - Return empty results for list operations

### Configuration Module Structure

```python
# src/common/s3_config.py
class S3Config:
    backend: str  # 'real', 'mock', 'skip'
    mock_root: str
    retry_config: dict
    credentials: dict
    
    @classmethod
    def from_environment(cls):
        # Implement selection logic
        pass
```

### Benefits
- Clear precedence rules
- Works on all platforms
- Supports testing without AWS
- Easy CI/CD integration
- Backward compatible with legacy USE_MOCK_S3