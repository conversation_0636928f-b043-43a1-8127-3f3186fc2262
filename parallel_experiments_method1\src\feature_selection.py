# feature_selection.py
"""
Feature selection utilities for group-based feature filtering
"""

import logging
import json
import os

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def select_features_by_group(feature_metadata, include_groups=None, exclude_groups=None):
    """
    Select feature indices based on group inclusion/exclusion criteria
    
    Args:
        feature_metadata: Dictionary containing feature metadata
        include_groups: List of group prefixes to include (None = include all)
        exclude_groups: List of group prefixes to exclude
        
    Returns:
        List of feature indices to use
    """
    all_features = feature_metadata['features']
    groups = feature_metadata['groups']
    
    # Start with all feature indices
    selected_indices = list(range(len(all_features)))
    
    # If include_groups is specified, only use features from those groups
    if include_groups is not None:
        selected_indices = []
        for group in include_groups:
            if group in groups:
                selected_indices.extend(groups[group])
            else:
                logger.warning(f"Include group '{group}' not found in metadata")
    
    # If exclude_groups is specified, remove features from those groups
    if exclude_groups is not None:
        excluded_indices = []
        for group in exclude_groups:
            if group in groups:
                excluded_indices.extend(groups[group])
            else:
                logger.warning(f"Exclude group '{group}' not found in metadata")
        
        # Remove excluded indices
        selected_indices = [idx for idx in selected_indices if idx not in excluded_indices]
    
    # Sort indices for consistency
    selected_indices = sorted(selected_indices)
    
    # Log detailed information about feature selection
    logger.info("="*60)
    logger.info("Feature Selection Summary:")
    logger.info(f"Total features available: {len(all_features)}")
    
    if include_groups is not None:
        logger.info(f"Include groups specified: {include_groups}")
    else:
        logger.info("Include groups: None (including all by default)")
    
    if exclude_groups is not None:
        logger.info(f"Exclude groups specified: {exclude_groups}")
    else:
        logger.info("Exclude groups: None")
    
    logger.info(f"Features selected: {len(selected_indices)}")
    
    # Log which features are being used
    logger.info("\nSelected features by group:")
    selected_by_group = {}
    for idx in selected_indices:
        feature = all_features[idx]
        group = feature['group'] if feature['group'] else '(no prefix)'
        if group not in selected_by_group:
            selected_by_group[group] = []
        selected_by_group[group].append(feature['name'])
    
    for group, features in sorted(selected_by_group.items()):
        logger.info(f"  {group}: {len(features)} features - {', '.join(features[:3])}{'...' if len(features) > 3 else ''}")
    
    # Log excluded features if any
    if exclude_groups:
        logger.info("\nExcluded features:")
        for group in exclude_groups:
            if group in groups:
                excluded_features = [all_features[idx]['name'] for idx in groups[group]]
                logger.info(f"  {group}: {len(excluded_features)} features - {', '.join(excluded_features[:3])}{'...' if len(excluded_features) > 3 else ''}")
    
    logger.info("="*60)
    
    return selected_indices

def load_feature_metadata(processed_dir='processed_data'):
    """
    Load feature metadata from JSON file
    
    Args:
        processed_dir: Directory containing processed data and metadata
        
    Returns:
        Dictionary containing feature metadata or None if not found
    """
    metadata_file = os.path.join(processed_dir, 'feature_metadata.json')
    if os.path.exists(metadata_file):
        with open(metadata_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    else:
        logger.warning(f"Feature metadata not found at {metadata_file}")
        return None