# Feature Metadata Workflow

## Important: Array Columns ARE Expanded in NPY Files

The preprocessing expands array columns (embeddings) into individual features:
- `user_embedding` (64-dim) → `user_embedding_0` to `user_embedding_63`
- `item_embedding` (64-dim) → `item_embedding_0` to `item_embedding_63`
- `context_embedding` (32-dim) → `context_embedding_0` to `context_embedding_31`

## Correct Workflow

1. **Run Data Analysis** (if needed):
   ```bash
   python src/analyze_data.py
   ```
   This creates `data_analysis/data_analysis_results.json`

2. **Run Preprocessing**:
   ```bash
   python src/run_parallel_processing.py
   ```
   This creates NPY files with expanded features (174 features total)

3. **Generate Feature Metadata**:
   ```bash
   python generate_feature_metadata_after_preprocess.py
   ```
   This creates `processed_data/feature_metadata_expanded.json`
 更新的文件：
    - src/train_loss_optimized.py - 现在使用expanded metadata
    - src/feature_selection.py - 现在使用expanded metadata
    - demo_feature_selection.py - 现在使用expanded metadata
    - src/save_feature_metadata.py - 保存到expanded metadata
    - verify_array_expansion.py - 读取expanded metadata
## File Structure

- **Correct metadata file**: `processed_data/feature_metadata_expanded.json`
- **Total features**: 174 (14 regular + 160 expanded from arrays)
- **All code now uses**: `feature_metadata_expanded.json`

## Feature Groups After Expansion

- **(no prefix)**: 8 features (basic features)
- **user**: 64 features (expanded embedding)
- **item**: 64 features (expanded embedding)
- **context**: 32 features (expanded embedding)
- **click**: 1 feature
- **session**: 1 feature
- **time**: 1 feature
- **income**: 1 feature
- **noise**: 2 features

## Code Updates Made

1. `src/train_loss_optimized.py` - Uses `feature_metadata_expanded.json`
2. `src/feature_selection.py` - Uses `feature_metadata_expanded.json`
3. `demo_feature_selection.py` - Uses `feature_metadata_expanded.json`
4. `src/save_feature_metadata.py` - Saves to `feature_metadata_expanded.json`

## Deprecated Files

- `DEPRECATED_generate_feature_metadata.py.bak` - Incorrectly assumed arrays were expanded
- `DEPRECATED_generate_correct_feature_metadata.py.bak` - Incorrectly assumed arrays were NOT expanded