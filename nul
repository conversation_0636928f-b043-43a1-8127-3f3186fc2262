.claude:
commands  hooks  settings.json	settings.local.json

artifacts:
audit_tree_count.tmp		 step5_diff.txt
step4_diff.txt			 step5_implement.log
step4_failure.log		 step5_plan.log
step4_format_fix_diff.txt	 step5_plan_detailed.log
step4_hotfix_diff.txt		 step5_posthoc_report.log
step4_hotfix_failure_retest.log  step5_training_output.log
step4_implement.log		 step5_zero_gap_diff.txt
step4_shortstat.txt		 step5_zero_gap_implement.log
step5_audit_fix.log		 step6_plan_detailed.log

parallel_experiments_method1:
__pycache__
artifacts
audit_tree_generator.py
audit_verify.py
benchmark_results
check_feature_dimensions.py
CLAUDE.md
CLOUD_DEPLOYMENT_FIXED.md
code_analysis_cc.md
Code_Understanding
Code_Understanding_2
Code_Understanding_aug2.zip
COMMANDS.md
compliance_report_20250731_003916.json
compliance_report_20250731_004019.json
compliance_report_20250731_004217.json
compliance_report_20250731_005826.json
compliance_report_20250731_005941.json
compliance_report_20250731_010100.json
compliance_report_20250804_101111.json
compliance_report_20250804_113729.json
compliance_report_20250804_115943.json
compliance_report_20250804_123843.json
compliance_report_20250804_124325.json
compliance_report_20250804_125203.json
compliance_report_20250804_130604.json
compliance_report_20250804_130745.json
compliance_report_20250804_132022.json
compliance_report_20250804_133642.json
compliance_report_20250804_135029.json
compliance_report_20250804_154348.json
compliance_report_20250804_154516.json
compliance_report_20250804_160300.json
compliance_report_20250804_164429.json
compliance_report_20250804_164931.json
compliance_report_20250804_172322.json
comprehensive_test_results
CROSS_PLATFORM_DEVELOPMENT_GUIDE.md
data_analysis
demo_feature_selection.py
deploy_ec2.sh
DEPLOYMENT_SUMMARY.md
DEPRECATED_generate_correct_feature_metadata.py.bak
DEPRECATED_generate_feature_metadata.py.bak
diagnose_multiprocessing.py
emergency_fix.py
env_config_examples.sh
feature_group_analysis.md
feature_manager_test_summary.md
FEATURE_METADATA_WORKFLOW.md
FEATURE_SELECTION_README.md
FINAL_SOLUTION.md
FINAL_TESTED_SOLUTION.md
fix_g5_parallel_processing.py
fix_parallel_worker_limit.py
FIX_UNICODE.md
fix_unicode_issues.py
generate_expanded_feature_metadata.py
generate_feature_metadata_after_preprocess.py
GPU_MIGRATION_COMPREHENSIVE_PLAN.md
GPU_MIGRATION_IMPLEMENTATION_REPORT.md
GPU迁移实施报告.md
gradient_history_dcnv2_balanced.png
gradient_history_dcnv2_sqrt_balanced.png
gradient_report_dcnv2_balanced.txt
gradient_report_dcnv2_sqrt_balanced.txt
install_gpu_deps.py
install_gpu_minimal.py
install_gpu_py39.bat
install_gpu_py39.py
install_gpu_requirements.bat
install_gpu_simple.py
local_test_data
loss_analysis_redesign_o3pro.md
loss_optimization_analysis_20250729.md
mlruns
nul
outofdate_old_documents
parallel_test_results
processed_data
PROJECT_DESIGN_DOCUMENT.md
quick_fix_parallel.sh
quick_test.py
README.md
README_METHOD1.md
README_update_summary_20250729.md
refactor_process
REFACTORING.md
requirements.txt
requirements_aws_ec2.txt
requirements_aws_verified.txt
requirements_conservative.txt
requirements_fixed.txt
requirements_gpu.txt
requirements_gpu_fixed.txt
requirements_gpu_py39.txt
requirements_minimal.txt
run_method1_mlflow_experiments.py
src
src_1stgpu_1230_0801
src_1stgpu_1230_0801.zip
src_gpu_featuregroup_0802
src_gpu_featuregroup_0802.zip
step5_training_test.log
STEP6_FINAL_COMPLIANCE_REPORT.md
test_detailed_tracking.py
test_expanded_feature_selection.py
test_feature_manager.py
test_feature_manager_comprehensive.py
test_feature_manager_examples.py
test_feature_selection.py
test_feature_selection_training.py
test_gpu_quick.py
test_gradient_history.png
test_gradient_report.txt
test_integrated_pipeline.py
test_logger_level.py
test_logs
test_metadata_generation.py
test_metadata_generation2.py
test_preprocess_arrays.py
test_results_20250731_001848.json
test_results_20250731_002005.json
test_s3_connection_debug.py
test_simple_preprocess.py
tests
tests.zip
venv
VENV_PATH.md
verify_array_expansion.py
windows_compatibility_results
worklog

src:
common

tests:
__init__.py  conftest.py  README.md	     test_parallel_contract.py
__pycache__  golden	  test_cli_smoke.py  test_train_contract.py
