#!/usr/bin/env python3
"""
Mock S3运行脚本 - 在Mock S3环境中运行并行处理
"""

import os
import sys
import tempfile
import logging
from pathlib import Path

# 设置环境变量启用Mock S3
os.environ['USE_MOCK_S3'] = 'true'
os.environ['TRAIN_DATA_DIR'] = 's3://test-bucket/train/'
os.environ['VALIDATION_DATA_DIR'] = 's3://test-bucket/validation/'
os.environ['TEST_DATA_DIR'] = 's3://test-bucket/test/'

try:
    import boto3
    import moto
    from moto import mock_aws
    import s3fs
    import pandas as pd
    import numpy as np
    MOCK_AVAILABLE = True
    print("✅ Mock S3依赖加载成功")
except ImportError as e:
    MOCK_AVAILABLE = False
    print(f"❌ Mock S3依赖不可用: {str(e)}")
    print("请安装: pip install moto boto3 s3fs")
    sys.exit(1)

def setup_mock_s3_data():
    """设置Mock S3环境和测试数据"""
    print("🔧 设置Mock S3环境...")
    
    # 创建S3客户端
    s3_client = boto3.client(
        's3',
        region_name='us-east-1',
        aws_access_key_id='testing',
        aws_secret_access_key='testing'
    )
    
    # 创建测试bucket
    bucket_name = 'test-bucket'
    s3_client.create_bucket(Bucket=bucket_name)
    print(f"✅ 创建Mock S3 bucket: {bucket_name}")
    
    # 生成测试数据并上传
    print("📊 生成测试数据...")
    datasets = {
        'train': {'num_files': 3, 'rows_per_file': 1000},
        'validation': {'num_files': 2, 'rows_per_file': 500},
        'test': {'num_files': 2, 'rows_per_file': 500}
    }
    
    temp_dir = tempfile.mkdtemp()
    
    for dataset, config in datasets.items():
        for i in range(config['num_files']):
            num_rows = config['rows_per_file']
            
            # 生成测试数据
            data = {
                'user_age': np.random.normal(35, 12, num_rows),
                'user_income': np.random.normal(50000, 15000, num_rows),
                'ad_category': np.random.choice(['electronics', 'fashion', 'food', 'travel'], num_rows),
                'click_history': np.random.poisson(5, num_rows),
                'ad_embedding': [np.random.normal(0, 1, 64).tolist() for _ in range(num_rows)],
                'click': np.random.binomial(1, 0.15, num_rows)  # 15%点击率
            }
            
            df = pd.DataFrame(data)
            
            # 保存为临时文件
            temp_file = os.path.join(temp_dir, f"temp_{dataset}_{i}.parquet")
            df.to_parquet(temp_file, index=False)
            
            # 上传到Mock S3
            s3_key = f"{dataset}/part-{i:05d}.parquet"
            s3_client.upload_file(temp_file, bucket_name, s3_key)
            
            print(f"  ✅ 上传 {s3_key} ({num_rows} 行)")
    
    print("✅ Mock S3数据设置完成")
    return s3_client

def run_with_mock_s3():
    """在Mock S3环境中运行并行处理"""
    print("🚀 启动Mock S3并行处理...")
    
    with mock_aws():
        # 设置Mock S3数据
        s3_client = setup_mock_s3_data()
        
        # 验证数据可访问性
        print("🔍 验证Mock S3数据...")
        try:
            response = s3_client.list_objects_v2(Bucket='test-bucket', Prefix='train/')
            files = [obj['Key'] for obj in response.get('Contents', [])]
            print(f"  ✅ 找到训练文件: {files}")
        except Exception as e:
            print(f"  ❌ 验证失败: {e}")
            return False
        
        # 导入并运行主处理脚本
        print("🔄 运行并行处理...")
        try:
            # 重新导入配置以应用环境变量
            import importlib
            if 'config' in sys.modules:
                importlib.reload(sys.modules['config'])
            
            from run_parallel_processing import main as run_main
            
            # 运行主处理流程
            success = run_main()
            
            if success:
                print("🎉 Mock S3并行处理完成！")
                return True
            else:
                print("❌ 并行处理失败")
                return False
                
        except Exception as e:
            print(f"❌ 运行过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 Mock S3并行处理启动器")
    print("=" * 60)
    
    if not MOCK_AVAILABLE:
        sys.exit(1)
    
    success = run_with_mock_s3()
    
    if success:
        print("\n✅ 所有操作完成！")
        sys.exit(0)
    else:
        print("\n❌ 操作失败！")
        sys.exit(1)
