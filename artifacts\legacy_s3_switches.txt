Legacy S3 Backend Switching Configuration
==========================================

Environment Variables:
- USE_MOCK_S3: Controls mock S3 mode (default: 'false')
  - When set to 'true', uses mock_s3 storage type
  - When 'false' or unset, uses real_s3 for S3 paths

Storage Type Detection Logic (in config.py):
1. Check if any path (TRAIN_DATA_DIR, VALIDATION_DATA_DIR, TEST_DATA_DIR) starts with 's3://'
2. If S3 paths detected:
   - If USE_MOCK_S3='true' → storage_type='mock_s3'
   - Otherwise → storage_type='real_s3'
3. If no S3 paths → storage_type='local'

Usage Pattern:
- STORAGE_TYPE variable holds the detected type ('mock_s3', 'real_s3', or 'local')
- USE_S3 boolean is True when STORAGE_TYPE is either 'mock_s3' or 'real_s3'

Key Findings:
- Mock implementation is controlled by environment variable only
- No actual mock backend implementation found (no moto, localstack, etc.)
- The 'mock_s3' type appears to be a placeholder - actual mock behavior not implemented
- All S3 operations use real s3fs library regardless of mock/real setting