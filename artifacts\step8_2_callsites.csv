file,line,function,type,current_implementation,s3_support_needed
preprocess.py,357,save_npy(),write,common_io.save_npy(),yes
preprocess.py,358,save_npy(),write,common_io.save_npy(),yes
preprocess.py,176,json.dump(),write,direct json.dump(),future
parallel_processor.py,358,save_npy(),write,common_io.save_npy(),yes
parallel_processor.py,359,save_npy(),write,common_io.save_npy(),yes
data_analyzer.py,489,json.dump(),write,direct json.dump(),future
save_feature_metadata.py,153,json.dump(),write,direct json.dump(),future
mlflow_config.py,145,torch.save(),write,direct torch.save(),no
mlflow_config.py,178,json.dump(),write,direct json.dump(),future

Summary:
- Total write operations: 9
- S3 write operations currently: 0
- save_npy via common_io: 4 (needs S3 support added)
- write_json potential: 4 (direct json.dump calls)
- torch.save: 1 (model specific, no S3 needed)
- s3_utils write functions: 0 (none implemented)